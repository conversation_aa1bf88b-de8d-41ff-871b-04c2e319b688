# SyncStockPluginComposite 类图设计文档

## 1. 类图概览

```puml
@startuml SyncStockPluginComposite类图

!define INTERFACE_COLOR #E1F5FE
!define ABSTRACT_COLOR #FFF3E0
!define CONCRETE_COLOR #E8F5E8
!define DATA_COLOR #F3E5F5

' 核心插件接口
interface ISyncStockPlugin <<interface>> INTERFACE_COLOR {
    +getName(): String
}

' 前置处理器接口
interface ISyncStockShopPreProcessor <<interface>> INTERFACE_COLOR {
    +preProcess(context: StockSyncContext, goodsMatch: GoodsMatchEnhance): StockContentResult<?>
    +supports(context: StockSyncContext, goodsMatch: GoodsMatchEnhance): boolean
}

' 结果处理器接口
interface ISyncStockShopResultProcessor <<interface>> INTERFACE_COLOR {
    +postProcess(context: StockSyncContext, resultPackage: StockSyncResultPackage): void
}

' 前置处理器抽象基类
abstract class AbstractSyncStockShopPreProcessor <<abstract>> ABSTRACT_COLOR {
    #log: Logger
    +preProcess(context: StockSyncContext, goodsMatch: GoodsMatchEnhance): StockContentResult<?>
    +getName(): String
    +supports(context: StockSyncContext, goodsMatch: GoodsMatchEnhance): boolean
    #{abstract} doPreProcess(context: StockSyncContext, goodsMatch: GoodsMatchEnhance): StockContentResult<?>
    #{abstract} caption(): String
}

' 结果处理器抽象基类
abstract class AbstractStockResultProcessor <<abstract>> ABSTRACT_COLOR {
    #log: Logger
    +getName(): String
    +postProcess(context: StockSyncContext, resultPackage: StockSyncResultPackage): void
    #justRunWhenFailed(): boolean
    #{abstract} processResults(context: StockSyncContext, resultPackage: StockSyncResultPackage): void
    #{abstract} caption(): String
}

' 插件组合管理类
class SyncStockPluginComposite CONCRETE_COLOR {
    -preProcessors: List<ISyncStockShopPreProcessor>
    -resultProcessors: List<ISyncStockShopResultProcessor>
    +SyncStockPluginComposite()
    +SyncStockPluginComposite(preProcessors: List<ISyncStockShopPreProcessor>, resultProcessors: List<ISyncStockShopResultProcessor>)
    +registerPreProcessor(preProcessor: ISyncStockShopPreProcessor): void
    +registerPreProcessors(preProcessors: List<ISyncStockShopPreProcessor>): void
    +processPreProcessors(context: StockSyncContext, goodsMatch: GoodsMatchEnhance): StockContentResult<?>
    +registerResultProcessor(resultProcessor: ISyncStockShopResultProcessor): void
    +registerResultProcessors(resultProcessors: List<ISyncStockShopResultProcessor>): void
    +processResultProcessors(context: StockSyncContext, resultPackage: StockSyncResultPackage): void
}

' 具体前置处理器实现
class ForbidSyncBizPreProcessor CONCRETE_COLOR {
    +doPreProcess(context: StockSyncContext, goodsMatch: GoodsMatchEnhance): StockContentResult<?>
    +caption(): String
}

' 具体结果处理器实现
class PersistStockResultProcessor CONCRETE_COLOR {
    +processResults(context: StockSyncContext, resultPackage: StockSyncResultPackage): void
    +caption(): String
}

class DisableStockResultProcessor CONCRETE_COLOR {
    +justRunWhenFailed(): boolean
    +processResults(context: StockSyncContext, resultPackage: StockSyncResultPackage): void
    +caption(): String
}

class TaoBaoCStoreFlagStockResultProcessor CONCRETE_COLOR {
    +processResults(context: StockSyncContext, resultPackage: StockSyncResultPackage): void
    +caption(): String
    -createCStoreFlag(composite: StockSyncResultComposite): void
    -forbidCStoreGoods(idEnhance: MatchIdEnhance, composite: StockSyncResultComposite): void
}

class TaoBaoFxStockResultProcessor CONCRETE_COLOR {
    +processResults(context: StockSyncContext, resultPackage: StockSyncResultPackage): void
    +caption(): String
}

' 数据类
class StockSyncContext DATA_COLOR {
    -vipUser: String
    -shopId: Integer
    -plat: PolyPlatEnum
    -shopBase: ApiShopBaseDto
    -syncStockConfig: SyncStockShopConfig
    -multiWhsAdapter: IMultiWarehouseAdapter
    -triggerType: StockSyncTriggerTypeEnum
}

class GoodsMatchEnhance DATA_COLOR {
    -matchId: Integer
    -sysMatch: ApiSysMatchDO
    -goodsInfo: GoodsInfo
}

class StockSyncResultPackage DATA_COLOR {
    -allSuccess: boolean
    -failedItems: Set<MatchIdEnhance>
    -needProcessErrorCodes: Set<String>
    -composites: Map<MatchIdEnhance, StockSyncResultComposite>
    +{static} create(composites: Map<MatchIdEnhance, StockSyncResultComposite>): StockSyncResultPackage
    +isAllSuccess(): boolean
    +getFailedItems(): Set<MatchIdEnhance>
    +getComposites(): Map<MatchIdEnhance, StockSyncResultComposite>
}

class "StockContentResult<T>" as StockContentResult DATA_COLOR {
    -success: boolean
    -message: String
    -data: T
    +{static} success(): StockContentResult<?>
    +{static} failed(message: String): StockContentResult<?>
    +isFailed(): boolean
}

' 继承关系
ISyncStockShopPreProcessor --|> ISyncStockPlugin
ISyncStockShopResultProcessor --|> ISyncStockPlugin
AbstractSyncStockShopPreProcessor ..|> ISyncStockShopPreProcessor
AbstractStockResultProcessor ..|> ISyncStockShopResultProcessor
ForbidSyncBizPreProcessor --|> AbstractSyncStockShopPreProcessor
PersistStockResultProcessor --|> AbstractStockResultProcessor
DisableStockResultProcessor --|> AbstractStockResultProcessor
TaoBaoCStoreFlagStockResultProcessor --|> AbstractStockResultProcessor
TaoBaoFxStockResultProcessor --|> AbstractStockResultProcessor

' 组合关系
SyncStockPluginComposite *-- ISyncStockShopPreProcessor : contains
SyncStockPluginComposite *-- ISyncStockShopResultProcessor : contains

' 依赖关系
SyncStockPluginComposite ..> StockSyncContext : uses
SyncStockPluginComposite ..> GoodsMatchEnhance : uses
SyncStockPluginComposite ..> StockSyncResultPackage : uses
SyncStockPluginComposite ..> StockContentResult : uses
ISyncStockShopPreProcessor ..> StockSyncContext : uses
ISyncStockShopPreProcessor ..> GoodsMatchEnhance : uses
ISyncStockShopPreProcessor ..> StockContentResult : uses
ISyncStockShopResultProcessor ..> StockSyncContext : uses
ISyncStockShopResultProcessor ..> StockSyncResultPackage : uses

@enduml
```

## 2. 核心类详细说明

### 2.1 插件接口层次

#### ISyncStockPlugin（顶层插件接口）
- **职责**: 定义所有库存同步插件的基本契约
- **方法**: 
  - `getName()`: 获取插件名称，用于标识和日志记录

#### ISyncStockShopPreProcessor（前置处理器接口）
- **职责**: 定义库存同步前置处理逻辑
- **继承**: ISyncStockPlugin
- **方法**:
  - `preProcess(context, goodsMatch)`: 执行前置处理逻辑
  - `supports(context, goodsMatch)`: 判断是否支持当前商品匹配

#### ISyncStockShopResultProcessor（结果处理器接口）
- **职责**: 定义库存同步结果后处理逻辑
- **继承**: ISyncStockPlugin
- **方法**:
  - `postProcess(context, resultPackage)`: 执行结果后处理逻辑

### 2.2 抽象基类

#### AbstractSyncStockShopPreProcessor（前置处理器抽象基类）
- **职责**: 提供前置处理器的通用实现框架
- **设计模式**: 模板方法模式
- **核心方法**:
  - `preProcess()`: 模板方法，定义处理流程骨架
  - `doPreProcess()`（抽象）: 具体处理逻辑，由子类实现
  - `caption()`（抽象）: 处理器标题，由子类实现
  - `supports()`: 默认支持所有商品，子类可重写

#### AbstractStockResultProcessor（结果处理器抽象基类）
- **职责**: 提供结果处理器的通用实现框架
- **设计模式**: 模板方法模式
- **核心方法**:
  - `postProcess()`: 模板方法，定义处理流程骨架
  - `processResults()`（抽象）: 具体处理逻辑，由子类实现
  - `caption()`（抽象）: 处理器标题，由子类实现
  - `justRunWhenFailed()`: 是否仅在失败时运行，默认false

### 2.3 核心管理类

#### SyncStockPluginComposite（插件组合管理器）
- **职责**: 统一管理和执行所有类型的库存同步插件
- **设计模式**: 组合模式 + 策略模式
- **核心属性**:
  - `preProcessors`: 前置处理器列表
  - `resultProcessors`: 结果处理器列表
- **核心方法**:
  - `registerPreProcessor()`: 注册单个前置处理器
  - `registerPreProcessors()`: 批量注册前置处理器
  - `processPreProcessors()`: 执行所有前置处理器
  - `registerResultProcessor()`: 注册单个结果处理器
  - `registerResultProcessors()`: 批量注册结果处理器
  - `processResultProcessors()`: 执行所有结果处理器

## 3. 具体实现类示例

### 3.1 前置处理器实现

#### ForbidSyncBizPreProcessor（禁止同步检查处理器）
- **功能**: 检查商品是否被标记为禁止库存同步
- **处理逻辑**: 检查商品匹配数据中的禁止同步标识
- **返回**: 如果禁止同步则返回失败结果，否则返回成功

### 3.2 结果处理器实现

#### PersistStockResultProcessor（结果持久化处理器）
- **功能**: 将库存同步结果持久化到数据库
- **处理逻辑**: 保存同步结果、更新商品状态、记录同步日志

#### DisableStockResultProcessor（停用库存同步处理器）
- **功能**: 在同步失败时停用商品的库存同步功能
- **特点**: 仅在存在失败结果时运行（justRunWhenFailed = true）

#### TaoBaoCStoreFlagStockResultProcessor（淘宝C店标记处理器）
- **功能**: 处理淘宝C店商品的特殊逻辑
- **处理逻辑**: 
  - 对C店商品进行特殊标记
  - 禁止特定错误类型的商品继续同步

#### TaoBaoFxStockResultProcessor（淘宝分销处理器）
- **功能**: 处理淘宝分销商品的特殊逻辑
- **处理逻辑**: 识别和处理分销商品异常情况

## 4. 数据流转类

### 4.1 输入数据类

#### StockSyncContext（库存同步上下文）
- **职责**: 携带库存同步过程中的上下文信息
- **核心属性**:
  - `vipUser`: 会员名
  - `shopId`: 店铺ID
  - `plat`: 平台类型
  - `shopBase`: 店铺基础信息
  - `syncStockConfig`: 库存同步配置
  - `multiWhsAdapter`: 多仓业务适配器
  - `triggerType`: 触发类型

#### GoodsMatchEnhance（增强商品匹配数据）
- **职责**: 携带商品匹配相关的详细信息
- **核心属性**:
  - `matchId`: 匹配ID
  - `sysMatch`: 系统匹配数据
  - `goodsInfo`: 商品信息

### 4.2 输出数据类

#### StockSyncResultPackage（库存同步结果包）
- **职责**: 封装库存同步的执行结果
- **核心属性**:
  - `allSuccess`: 是否全部成功
  - `failedItems`: 失败项集合
  - `needProcessErrorCodes`: 需要处理的错误码
  - `composites`: 详细结果组合

#### StockContentResult<T>（通用结果包装类）
- **职责**: 统一的结果返回格式
- **核心属性**:
  - `success`: 是否成功
  - `message`: 结果消息
  - `data`: 结果数据

## 5. 设计模式应用

### 5.1 组合模式（Composite Pattern）
- **应用场景**: SyncStockPluginComposite管理多个插件
- **优势**: 客户端统一接口，无需关心具体插件实现

### 5.2 策略模式（Strategy Pattern）
- **应用场景**: 不同插件实现不同的处理策略
- **优势**: 可动态组合不同策略，扩展性强

### 5.3 模板方法模式（Template Method Pattern）
- **应用场景**: 抽象基类定义处理流程骨架
- **优势**: 统一处理流程，子类专注业务逻辑

## 6. 核心业务逻辑

### 6.1 前置处理逻辑流程
```
1. 检查是否有注册的前置处理器
2. 遍历所有前置处理器
3. 对每个处理器：
   - 检查是否支持当前商品（supports方法）
   - 执行具体处理逻辑（doPreProcess方法）
   - 如果返回失败，立即停止并返回失败结果（快速失败策略）
4. 异常处理：捕获并记录异常，返回失败结果
5. 所有处理器成功时返回成功结果
```

### 6.2 结果处理逻辑流程
```
1. 检查是否有注册的结果处理器
2. 遍历所有结果处理器
3. 对每个处理器：
   - 检查是否仅在失败时运行（justRunWhenFailed方法）
   - 如果条件满足，执行具体处理逻辑（processResults方法）
   - 异常处理：捕获并记录异常，但继续执行后续处理器（容错策略）
4. 无返回值，专注于副作用操作
```

## 7. 关键设计特性

### 7.1 可扩展性
- 通过实现相应接口可以轻松添加新的插件
- 插件之间相互独立，不影响现有功能

### 7.2 灵活性
- 支持动态注册和组合插件
- 可根据业务需求选择性启用插件

### 7.3 容错性
- 前置处理器采用快速失败机制，确保数据一致性
- 结果处理器采用容错机制，单个失败不影响整体

### 7.4 可维护性
- 清晰的接口层次和职责分离
- 统一的异常处理和日志记录

### 7.5 性能考虑
- 插件按顺序执行，避免并发复杂性
- 支持条件执行，减少不必要的处理开销

这种设计使得库存同步系统具有很好的扩展性和维护性，可以根据不同平台和业务需求灵活组合各种处理逻辑。
