package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch.data.CommonWarehouseMatchDTO;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockGoodsExtRuleTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.multi.adapter.core.IMultiWarehouseAdapter;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockNumRuleDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ShopConfigStockSyncRuleEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.SyncStockShopConfigUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * StockCalculationRule 单元测试
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public class StockCalculationRuleTest {

    @Mock
    private IMultiWarehouseAdapter mockMultiWhsAdapter;

    private StockSyncContext context;
    private GoodsMatchEnhance goodsMatchEnhance;
    private ApiSysMatchDO apiSysMatch;
    private SyncStockShopConfig syncStockConfig;
    private SyncStockNumRuleDto syncNumRule;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 创建测试上下文
        context = createTestContext();
        
        // 创建匹配数据
        goodsMatchEnhance = createGoodsMatchEnhance();
        apiSysMatch = goodsMatchEnhance.getSysMatch();
        
        // 创建配置
        syncStockConfig = context.getSyncStockConfig();
        syncNumRule = createSyncNumRule();
        syncStockConfig.setSyncNumRule(syncNumRule);
    }

    /**
     * 测试基本信息设置
     */
    @Test
    public void testBasicInfoSetting() {
        // 设置基本信息
        apiSysMatch.setGoodsID(1001);
        apiSysMatch.setSpecID(2001);
        apiSysMatch.setGoodsType(1);
        apiSysMatch.setShopId(3001);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_ONE);

            // 执行测试
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // 验证结果
            assertEquals(Integer.valueOf(1001), rule.getErpGoodsId());
            assertEquals(Integer.valueOf(2001), rule.getErpSpecId());
            assertEquals(Integer.valueOf(1), rule.getGoodsType());
            assertEquals(Integer.valueOf(3001), rule.getShopId());
            assertEquals(ShopConfigStockSyncRuleEnum.RULE_ONE, rule.getShopConfigStockSyncRule());
            assertTrue(rule.isbSyncPctStock());
        }
    }

    /**
     * 测试匹配级百分比优先级
     */
    @Test
    public void testMatchLevelPercentagePriority() {
        // 设置匹配级百分比
        apiSysMatch.setbSingletb(true);
        apiSysMatch.setSingleNumPer(80);
        syncNumRule.setSyncNumPercentage(60);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // 执行测试
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // 验证匹配级百分比优先
            assertEquals(BigDecimal.valueOf(80), rule.getPercent());
        }
    }

    /**
     * 测试店铺级百分比设置
     */
    @Test
    public void testShopLevelPercentageSetting() {
        // 设置店铺级百分比
        apiSysMatch.setBSingletb(false);
        syncNumRule.setSyncNumPercentage(70);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // 执行测试
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // 验证店铺级百分比
            assertEquals(BigDecimal.valueOf(70), rule.getPercent());
        }
    }

    /**
     * 测试固定数量规则设置
     */
    @Test
    public void testFixedQuantityRuleSetting() {
        // 设置固定数量规则
        apiSysMatch.setBruleStop(true);
        apiSysMatch.setbFixNum(1); // 固定数量模式
        apiSysMatch.setFixNum(100);
        apiSysMatch.setVirNumBase(10);
        apiSysMatch.setVirNumTop(200);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // 执行测试
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // 验证固定数量规则
            assertEquals(StockGoodsExtRuleTypeEnum.FIX, rule.getExtRuleType());
            assertEquals(BigDecimal.valueOf(100), rule.getFixedQuantity());
            assertEquals(BigDecimal.valueOf(10), rule.getMinStockQuantity());
            assertEquals(BigDecimal.valueOf(200), rule.getMaxStockQuantity());
        }
    }

    /**
     * 测试条件固定数量规则设置
     */
    @Test
    public void testConditionFixedQuantityRuleSetting() {
        // 设置条件固定数量规则
        apiSysMatch.setBruleStop(true);
        apiSysMatch.setbFixNum(2); // 条件固定数量模式
        apiSysMatch.setFixNum(50);
        apiSysMatch.setVirNumBase(5);
        apiSysMatch.setVirNumTop(150);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // 执行测试
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // 验证条件固定数量规则
            assertEquals(StockGoodsExtRuleTypeEnum.CONDITION_FIX, rule.getExtRuleType());
            assertEquals(BigDecimal.valueOf(50), rule.getFixedQuantity());
            assertEquals(BigDecimal.valueOf(5), rule.getMinStockQuantity());
            assertEquals(BigDecimal.valueOf(150), rule.getMaxStockQuantity());
        }
    }

    /**
     * 测试条件增加数量规则设置
     */
    @Test
    public void testConditionIncrementalQuantityRuleSetting() {
        // 设置条件增加数量规则
        apiSysMatch.setBruleStop(true);
        apiSysMatch.setBVirNum(true);
        apiSysMatch.setVirNumInc(20);
        apiSysMatch.setVirNumBase(10);
        apiSysMatch.setVirNumTop(100);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // 执行测试
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // 验证条件增加数量规则
            assertEquals(StockGoodsExtRuleTypeEnum.CONDITION_FIX_INCREASE, rule.getExtRuleType());
            assertEquals(BigDecimal.valueOf(20), rule.getIncrementalQuantity());
            assertEquals(BigDecimal.valueOf(10), rule.getMinStockQuantity());
            assertEquals(BigDecimal.valueOf(100), rule.getMaxStockQuantity());
        }
    }

    /**
     * 测试匹配级仓库优先级
     */
    @Test
    public void testMatchLevelWarehousePriority() {
        // 设置匹配级仓库
        apiSysMatch.setRuleWarehouse("1001,1002,1003");
        goodsMatchEnhance.setMultiSign("MULTI_SIGN_TEST");

        // 设置平台仓库匹配
        Set<CommonWarehouseMatchDTO> whsMatches = new HashSet<>();
        CommonWarehouseMatchDTO whsMatch = new CommonWarehouseMatchDTO();
        whsMatch.setErpWarehouseIds(Arrays.asList(1001, 1004, 1005));
        whsMatches.add(whsMatch);

        when(mockMultiWhsAdapter.getWhsMatchesByPlat(any(), eq("MULTI_SIGN_TEST"))).thenReturn(whsMatches);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // 执行测试
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // 验证匹配级仓库和平台仓库的交集
            assertEquals(1, rule.getWarehouseCodes().size());
            assertTrue(rule.getWarehouseCodes().contains(1001));
        }
    }

    /**
     * 测试平台仓库匹配
     */
    @Test
    public void testPlatformWarehouseMatching() {
        // 设置平台仓库匹配
        goodsMatchEnhance.setMultiSign("MULTI_SIGN_TEST");

        Set<CommonWarehouseMatchDTO> whsMatches = new HashSet<>();
        CommonWarehouseMatchDTO whsMatch = new CommonWarehouseMatchDTO();
        whsMatch.setErpWarehouseIds(Arrays.asList(2001, 2002, 2003));
        whsMatches.add(whsMatch);

        when(mockMultiWhsAdapter.getWhsMatchesByPlat(any(), eq("MULTI_SIGN_TEST"))).thenReturn(whsMatches);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // 执行测试
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // 验证平台仓库
            assertEquals(3, rule.getWarehouseCodes().size());
            assertTrue(rule.getWarehouseCodes().containsAll(Arrays.asList(2001, 2002, 2003)));
        }
    }

    /**
     * 测试店铺级仓库设置
     */
    @Test
    public void testShopLevelWarehouseSetting() {
        // 设置店铺级仓库
        syncNumRule.setWarehouseIds(Arrays.asList("3001", "3002", "3003"));

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // 执行测试
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // 验证店铺级仓库
            assertEquals(3, rule.getWarehouseCodes().size());
            assertTrue(rule.getWarehouseCodes().containsAll(Arrays.asList(3001, 3002, 3003)));
        }
    }

    /**
     * 测试默认店铺配置规则
     */
    @Test
    public void testDefaultShopConfigRule() {
        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(null);

            // 执行测试
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // 验证默认规则
            assertEquals(ShopConfigStockSyncRuleEnum.RULE_THREE, rule.getShopConfigStockSyncRule());
        }
    }

    //region 私有方法
    /**
     * 创建测试上下文
     */
    private StockSyncContext createTestContext() {
        StockSyncContext context = new StockSyncContext();
        context.setVipUser("testUser");
        context.setShopId(12345);
        context.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        context.setMultiWhsAdapter(mockMultiWhsAdapter);
        
        ApiShopBaseDto shopBase = new ApiShopBaseDto();
        shopBase.setShopId(12345);
        shopBase.setShopName("测试店铺");
        shopBase.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        context.setShopBase(shopBase);
        
        SyncStockShopConfig syncConfig = new SyncStockShopConfig();
        syncConfig.setIsSyncMoreZeroAutoShelves(true);
        context.setSyncStockConfig(syncConfig);
        
        return context;
    }

    /**
     * 创建商品匹配数据
     */
    private GoodsMatchEnhance createGoodsMatchEnhance() {
        ApiSysMatchDO apiSysMatch = new ApiSysMatchDO();
        apiSysMatch.setGoodsID(1001);
        apiSysMatch.setSpecID(2001);
        apiSysMatch.setGoodsType(1);
        apiSysMatch.setShopId(12345);
        
        GoodsMatchEnhance enhance = new GoodsMatchEnhance();
        enhance.setSysMatch(apiSysMatch);
        enhance.setShopId(12345);
        
        return enhance;
    }

    /**
     * 创建同步数量规则
     */
    private SyncStockNumRuleDto createSyncNumRule() {
        SyncStockNumRuleDto rule = new SyncStockNumRuleDto();
        rule.setSyncNumPercentage(50);
        rule.setWarehouseIds(Arrays.asList("101", "102", "103"));
        return rule;
    }
    //endregion
}
