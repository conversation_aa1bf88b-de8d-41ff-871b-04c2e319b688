package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule;

import com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch.data.CommonWarehouseMatchDTO;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockGoodsCalculationModeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockGoodsExtRuleTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.multi.adapter.core.IMultiWarehouseAdapter;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockNumRuleDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ShopConfigStockSyncRuleEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.SyncStockShopConfigUtils;
import com.mchange.lang.IntegerUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存计算规则
 * 基于匹配信息 + 店铺配置 + 多仓信息构建基础计算规则
 *
 * <AUTHOR>
 * @date 2024-12-26 10:00
 */
public class StockCalculationRule {
    //region 属性
    /**
     * 计算模式
     */
    private StockGoodsCalculationModeEnum calculationMode;

    //region 计算库存基本信息
    /**
     * 仓库代码列表
     */
    private List<Integer> warehouseCodes;

    /**
     * ERP商品ID
     */
    private Integer erpGoodsId;

    /**
     * ERP规格ID
     */
    private Integer erpSpecId;

    /**
     * 是否组合装
     */
    private Integer goodsType;

    /**
     * 管家店铺ID
     */
    private Integer shopId;

    /**
     * 店铺配制计算规则
     */
    private ShopConfigStockSyncRuleEnum shopConfigStockSyncRule;
    //endregion

    //region 计算规则
    /**
     * 同步百分比
     */
    private BigDecimal percent;

    /**
     * 额外规则类别
     */
    private StockGoodsExtRuleTypeEnum extRuleType;

    /**
     * 同步固定数量
     */
    private BigDecimal fixedQuantity;

    /**
     * 同步增加数量
     */
    private BigDecimal incrementalQuantity;

    /**
     * 库存同步区间 - 最小值
     */
    private BigDecimal minStockQuantity;

    /**
     * 库存同步区间 - 最大值
     */
    private BigDecimal maxStockQuantity;

    /**
     * 是否向上取整
     */
    private boolean bSyncPctStock;
    //endregion

    //endregion

    //region 构造方法

    /**
     * 私有构造方法
     */
    private StockCalculationRule() {
    }
    //endregion

    //region 公共静态方法

    /**
     * 创建计算规则
     *
     * @param goodsMatchEnhance 匹配数据
     * @param context           上下文
     * @return 计算规则
     */
    public static StockCalculationRule create(GoodsMatchEnhance goodsMatchEnhance, StockSyncContext context) {
        // 基础信息
        SyncStockShopConfig syncStockConfig = context.getSyncStockConfig();
        IMultiWarehouseAdapter multiWhsAdapter = context.getMultiWhsAdapter();
        ApiSysMatchDO goodsMatch = goodsMatchEnhance.getSysMatch();
        SyncStockNumRuleDto syncNumRule = syncStockConfig.getSyncNumRule();

        // 构建规则
        StockCalculationRule rule = new StockCalculationRule();
        // 从匹配数据信息
        Integer matchPercent = 0;
        if (goodsMatch != null) {
            // erp商品信息
            rule.erpGoodsId = goodsMatch.getGoodsID();
            rule.erpSpecId = goodsMatch.getSpecID();
            rule.goodsType = goodsMatch.getGoodsType();
            rule.shopId = goodsMatch.getShopId();

            // 匹配级特定规则
            if (goodsMatch.getBruleStop()) {
                // 是否同步固定数量
                if (goodsMatch.getbFixNum() > 0) {
                    rule.fixedQuantity = BigDecimal.valueOf(goodsMatch.getFixNum());
                    rule.minStockQuantity = BigDecimal.valueOf(goodsMatch.getVirNumBase());
                    rule.maxStockQuantity = BigDecimal.valueOf(goodsMatch.getVirNumTop());
                    switch (goodsMatch.getbFixNum()) {
                        case 1:
                            rule.extRuleType = StockGoodsExtRuleTypeEnum.FIX;
                            break;
                        case 2:
                            rule.extRuleType = StockGoodsExtRuleTypeEnum.CONDITION_FIX;
                            break;
                    }
                }
                // 是否固定增加库存
                if (goodsMatch.getbVirNum()) {
                    rule.incrementalQuantity = BigDecimal.valueOf(goodsMatch.getVirNumInc());
                    rule.minStockQuantity = BigDecimal.valueOf(goodsMatch.getVirNumBase());
                    rule.maxStockQuantity = BigDecimal.valueOf(goodsMatch.getVirNumTop());
                    rule.extRuleType = StockGoodsExtRuleTypeEnum.CONDITION_FIX_INCREASE;
                }
            }
            // 匹配级百分比
            if (goodsMatch.getbSingletb()) {
                matchPercent = goodsMatch.getSingleNumPer();
            }
        }

        // 同步百分比 优先级：匹配级 > 店铺级
        if (matchPercent > 0 || (syncNumRule != null && syncNumRule.getSyncNumPercentage() > 0)) {
            rule.percent = matchPercent > 0
                    ? BigDecimal.valueOf(matchPercent)
                    : BigDecimal.valueOf(syncNumRule.getSyncNumPercentage());
        }
        // erp仓库Id 优先级：匹配级 > 平台仓库匹配 > 店铺级
        rule.warehouseCodes = rule.getErpWarehouseIds(goodsMatchEnhance, syncStockConfig, multiWhsAdapter);
        // 是否向上取整
        rule.bSyncPctStock = syncStockConfig.getIsSyncMoreZeroAutoShelves();
        // 店铺配制同步规则
        ShopConfigStockSyncRuleEnum shopConfigStockSyncRuleEnum = SyncStockShopConfigUtils.getShopConfigStockSyncRule(syncStockConfig);
        if(shopConfigStockSyncRuleEnum == null){
            shopConfigStockSyncRuleEnum = ShopConfigStockSyncRuleEnum.RULE_THREE;
        }
        rule.shopConfigStockSyncRule = shopConfigStockSyncRuleEnum;
        return rule;
    }
    //endregion

    //region 私有方法
    /**
     * 获取erp仓库Ids
     *
     * @param goodsMatchEnhance 匹配数据
     * @param syncStockConfig   库存同步店铺配制
     * @param multiWhsAdapter   多仓适配器
     * @return erp仓库Ids
     */
    private List<Integer> getErpWarehouseIds(GoodsMatchEnhance goodsMatchEnhance, SyncStockShopConfig syncStockConfig, IMultiWarehouseAdapter multiWhsAdapter) {
        // 基础信息
        ApiSysMatchDO goodsMatch = goodsMatchEnhance.getSysMatch();
        SyncStockNumRuleDto syncNumRule = syncStockConfig.getSyncNumRule();
        // 匹配级仓库
        List<Integer> matchWarehouseIds = new ArrayList<>();
        if (goodsMatch != null && StringUtils.isNotEmpty(goodsMatch.getRuleWarehouse())) {
            List<Integer> warehouseIds = Arrays.stream(StringUtils.split(goodsMatch.getRuleWarehouse(), ","))
                    .map(x -> IntegerUtils.parseInt(x, 0)).filter(x -> x > 0).distinct().collect(Collectors.toList());
            matchWarehouseIds.addAll(warehouseIds);
        }
        // 获取平台仓库erp仓库列表
        List<Integer> platErpWarehouseIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(goodsMatchEnhance.getMultiSign())) {
            Set<CommonWarehouseMatchDTO> whsMatchesByPlat = multiWhsAdapter.getWhsMatchesByPlat(goodsMatch, goodsMatchEnhance.getMultiSign());
            whsMatchesByPlat.forEach(x -> platErpWarehouseIds.addAll(x.getErpWarehouseIds()));
        }

        // 平台仓库匹配 存在匹配级仓库时，优先过滤匹配级仓库
        if (CollectionUtils.isNotEmpty(platErpWarehouseIds)) {
            return CollectionUtils.isNotEmpty(matchWarehouseIds)
                    ? platErpWarehouseIds.stream().filter(matchWarehouseIds::contains).collect(Collectors.toList())
                    : platErpWarehouseIds;
        }
        // 匹配级仓库
        if (CollectionUtils.isNotEmpty(matchWarehouseIds)) {
            return matchWarehouseIds;
        }
        // 店铺级erp仓库列表
        if (syncNumRule != null) {
            return syncNumRule.getWarehouseIds().stream().map(x -> IntegerUtils.parseInt(x, 0)).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    //endregion

    //region getter
    public StockGoodsCalculationModeEnum getCalculationMode() {
        return calculationMode;
    }

    public List<Integer> getWarehouseCodes() {
        return warehouseCodes;
    }

    public Integer getErpGoodsId() {
        return erpGoodsId;
    }

    public Integer getErpSpecId() {
        return erpSpecId;
    }

    public Integer getGoodsType() {
        return goodsType;
    }

    public Integer getShopId() {
        return shopId;
    }

    public ShopConfigStockSyncRuleEnum getShopConfigStockSyncRule() {
        return shopConfigStockSyncRule;
    }

    public BigDecimal getPercent() {
        return percent;
    }

    public StockGoodsExtRuleTypeEnum getExtRuleType() {
        return extRuleType;
    }

    public BigDecimal getFixedQuantity() {
        return fixedQuantity;
    }

    public BigDecimal getIncrementalQuantity() {
        return incrementalQuantity;
    }

    public BigDecimal getMinStockQuantity() {
        return minStockQuantity;
    }

    public BigDecimal getMaxStockQuantity() {
        return maxStockQuantity;
    }

    public boolean isbSyncPctStock() {
        return bSyncPctStock;
    }
    //endregion
}
