package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockRequestGoodInfo;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.WdgjGoodsTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncBuildRequestResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncDoRequestResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncGoodsRequestPair;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncMatchExecuteResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncMatchOutResultDetail;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.instance.StockCalculationInstance;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode.IStockCalculationMode;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode.plugins.FitStockCalculationMode;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode.plugins.NormalStockCalculationMode;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule.StockCalculationRule;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy.IStockCalculationStrategy;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy.plugins.DefaultStockCalculationStrategy;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.core.ISyncStockProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation.StockSyncRequestOperation;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plugin.SyncStockPluginComposite;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plugin.preprocess.ISyncStockShopPreProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plugin.preprocess.plugins.ForbidSyncBizPreProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;

import java.util.ArrayList;
import java.util.List;

/**
 * 库存同步-业务处理基础实现
 *
 * <AUTHOR>
 * @date 2024-02-26 9:31
 */
public class BaseSyncStockProcessor implements ISyncStockProcessor {
    //region 变量
    /**
     * 日志
     */
    protected final String caption = "库存同步业务抽象";

    /**
     * 全局上下文
     */
    protected final StockSyncContext context;

    /**
     * 插件组合类
     */
    protected final SyncStockPluginComposite pluginComposite;

    /**
     * 库存同步请求操作
     */
    protected final StockSyncRequestOperation stockSyncRequestOperation;

    //endregion

    // region 构造方法

    /**
     * 构造函数
     *
     * @param context 全局上下文
     */
    public BaseSyncStockProcessor(StockSyncContext context) {
        this.context = context;
        this.pluginComposite = initPluginComposite();
        this.stockSyncRequestOperation = new StockSyncRequestOperation(context);
    }

    // endregion

    //region 库存同步核心步骤

    /**
     * 构建库存同步请求
     *
     * @param goodsMatches 商品匹配列表
     * @return 库存同步请求信息
     */
    @Override
    public final StockSyncBuildRequestResult buildSyncStockRequests(List<GoodsMatchEnhance> goodsMatches) {
        // 初始化
        List<StockSyncGoodsRequestPair> goodsRequests = new ArrayList<>();
        List<StockSyncMatchExecuteResult> buildFailedResults = new ArrayList<>();

        // 遍历匹配数据
        goodsMatches.forEach(goodsMatch -> {
            try {
                // 1、店铺级前置处理 - 插件
                StockContentResult<?> preCheckResult = preCheck(goodsMatch);
                if (preCheckResult.isFailed()) {
                    buildFailedResults.add(StockSyncMatchExecuteResult.filedResult(goodsMatch, preCheckResult.getMessage()));
                    return;
                }

                // 2、库存量计算
                GoodsStockCalculationResult stockOutput = this.calculateSyncQuantity(goodsMatch);
                if (stockOutput.isFailed()) {
                    buildFailedResults.add(StockSyncMatchExecuteResult.filedResult(goodsMatch, preCheckResult.getMessage()));
                    return;
                }

                // 3、构建商品级请求
                StockSyncGoodsRequestPair stockSyncGoodsRequestPair = stockSyncRequestOperation.formatGoodsRequestPair(goodsMatch, stockOutput);
                // 平台级特殊处理
                StockContentResult<?> requestResult = specialHandleRequest(goodsMatch, stockOutput, stockSyncGoodsRequestPair.getGoodsInfo());
                if (requestResult.isFailed()) {
                    buildFailedResults.add(StockSyncMatchExecuteResult.filedResult(goodsMatch, preCheckResult.getMessage()));
                    return;
                }
                goodsRequests.add(stockSyncGoodsRequestPair);
            } catch (Exception e) {
                String message = String.format("【%s-%s】构建库存同步请求异常：%s", goodsMatch.getSysMatch().getId(), goodsMatch.getMultiSign(), e.getMessage());
                buildFailedResults.add(StockSyncMatchExecuteResult.filedResult(goodsMatch, message));
                LogFactory.error(caption, message, e);
            }
        });

        return StockSyncBuildRequestResult.success(buildFailedResults, goodsRequests);
    }

    /**
     * 发起菠萝派库存同步
     *
     * @param requestGoodsItems 请求商品列表
     * @return 请求结果
     */
    @Override
    public final StockSyncDoRequestResult doRequest(List<StockSyncGoodsRequestPair> requestGoodsItems) {
        // 1、拆分商品级并构建请求

        // 2、根据请求模式发起库存同步请求

        // 3、菠萝派结果处理

        return new StockSyncDoRequestResult();
    }

    /**
     * 保存库存同步结果
     *
     * @param executeResults 库存同步执行结果
     * @return 结果
     */
    @Override
    public final List<StockSyncMatchOutResultDetail> saveSyncStockResult(
            List<StockSyncMatchExecuteResult> executeResults) {
        return new ArrayList<>();
    }

    //endregion

    //region 供子类重写方法

    //region 前置校验

    /**
     * 前置校验
     *
     * @param goodsMatch 匹配数据
     * @return 校验结果
     */
    protected StockContentResult<?> preCheck(GoodsMatchEnhance goodsMatch) {
        // 使用插件组合类执行所有前置处理器
        return pluginComposite.processPreProcessors(context, goodsMatch);
    }

    /**
     * 获取平台特定前置处理器
     * 子类可以重写此方法添加平台特定的前置处理器
     *
     * @return 平台特定前置处理器列表
     */
    protected List<ISyncStockShopPreProcessor> getPlatPreProcessors() {
        return new ArrayList<>();
    }
    //endregion

    //region 计算同步数量

    /**
     * 计算同步数量
     *
     * @param goodsMatch 匹配数据
     * @return 结果
     */
    protected GoodsStockCalculationResult calculateSyncQuantity(GoodsMatchEnhance goodsMatch) {
        // 构建匹配级计算规则
        StockCalculationRule rule = StockCalculationRule.create(goodsMatch, context);
        // 获取库存计算模式和库存计算策略
        IStockCalculationMode stockCalculationMode = getStockCalculationMode(goodsMatch);
        IStockCalculationStrategy stockCalculationStrategy = getStockCalculationStrategy(rule);
        // 计算数量
        StockCalculationInstance stockCalculationInstance = new StockCalculationInstance(context, stockCalculationMode, stockCalculationStrategy);
        return stockCalculationInstance.calculateSyncQuantity(rule);
    }

    /**
     * 获取库存计算模式
     *
     * @param goodsMatch 匹配信息
     * @return 库存计算模式
     */
    protected final IStockCalculationMode getStockCalculationMode(GoodsMatchEnhance goodsMatch) {

        ApiSysMatchDO sysMatch = goodsMatch.getSysMatch();
        WdgjGoodsTypeEnum wdgjGoodsType = WdgjGoodsTypeEnum.create(sysMatch.getGoodsType());
        if (wdgjGoodsType != null) {
            switch (wdgjGoodsType) {
                case Standard:
                case BatchGoods:
                    return new NormalStockCalculationMode(context);
                case AssembleGoods:
                    return new FitStockCalculationMode(context);
            }
        }
        throw new IllegalArgumentException("未实现的库存计算模式");
    }

    /**
     * 获取库存计算策略
     *
     * @param goodsMatch 匹配信息
     * @return 库存计算策略
     */
    protected final IStockCalculationStrategy getStockCalculationStrategy(GoodsMatchEnhance goodsMatch) {
        return new DefaultStockCalculationStrategy(context);
    }
    //endregion

    //region 构建商品级请求

    /**
     * 构建商品级请求
     *
     * @param goodsMatch 匹配数据
     * @return 结果
     */
    protected StockContentResult<?> specialHandleRequest(GoodsMatchEnhance goodsMatch, GoodsStockCalculationResult goodsStockInfo, BusinessBatchSyncStockRequestGoodInfo goodsRequest) {
        return StockContentResult.success();
    }
    //endregion

    //endregion

    //region 私有方法

    /**
     * 初始化插件组合类
     *
     * @return 插件组合类
     */
    private SyncStockPluginComposite initPluginComposite() {
        SyncStockPluginComposite composite = new SyncStockPluginComposite();

        // 注册通用前置处理器
        composite.registerPreProcessor(new ForbidSyncBizPreProcessor());

        // 注册平台特定前置处理器
        List<ISyncStockShopPreProcessor> platPreProcessors = getPlatPreProcessors();
        composite.registerPreProcessors(platPreProcessors);

        return composite;
    }

    //endregion
}
